package com.hnguigu.springcloud.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.reactive.CorsWebFilter;
import org.springframework.web.cors.reactive.UrlBasedCorsConfigurationSource;

import java.time.Duration;
import java.util.Arrays;

/**
 * Gateway 跨域配置
 * 这是另一种配置跨域的方式，可以与 application.yml 中的配置二选一使用
 */
@Configuration
public class CorsConfig {

    /**
     * 配置跨域过滤器
     * 注意：如果使用了这个配置，建议注释掉 application.yml 中的 globalcors 配置
     */
    // @Bean
    public CorsWebFilter corsWebFilter() {
        CorsConfiguration config = new CorsConfiguration();

        // 允许的源
        // 生产环境建议指定具体的域名，而不是使用 "*"
        config.addAllowedOrigin("*");
        // 如果需要指定具体域名：
        // config.addAllowedOrigin("http://localhost:3000");
        // config.addAllowedOrigin("https://yourdomain.com");

        // 允许的HTTP方法
        config.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH"));

        // 允许的请求头
        config.addAllowedHeader("*");

        // 是否允许携带认证信息（cookies、authorization headers等）
        config.setAllowCredentials(true);

        // 预检请求的缓存时间
        config.setMaxAge(Duration.ofHours(1));

        // 暴露给客户端的响应头
        
        config.addExposedHeader("Content-Length");
        config.addExposedHeader("Access-Control-Allow-Origin");
        config.addExposedHeader("Access-Control-Allow-Headers");
        config.addExposedHeader("Cache-Control");
        config.addExposedHeader("Content-Language");
        config.addExposedHeader("Content-Type");
        config.addExposedHeader("Expires");
        config.addExposedHeader("Last-Modified");
        config.addExposedHeader("Pragma");

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", config);


        return new CorsWebFilter(source);
    }
}
