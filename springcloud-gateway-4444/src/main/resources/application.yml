server:
  port: 4444
spring:
  application:
    name: gateway-service
  redis:
    host: localhost
    port: 6379
  cloud:
    sentinel:
      transport:
        dashboard: localhost:8081
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
    gateway:
      globalcors:
        cors-configurations:
          [ /** ]:
            # 允许的源，不能与 allowCredentials: true 同时使用 "*"
            allowedOriginPatterns: "*"
            # 允许的HTTP方法
            allowedMethods:
              - GET
              - POST
              - PUT
              - DELETE
              - OPTIONS
              - HEAD
              - PATCH
            # 允许的请求头
            allowedHeaders: "*"
            # 是否允许携带认证信息（如 cookies）
            allowCredentials: true
            # 预检请求的缓存时间（秒）
            maxAge: 3600
      routes:
        - id: orderRouter
          predicates:
            - Path=/api/orders/**     # Path断言。当url地址是以/api/orders开头就满足条件
          #          uri: http://localhost:8888    # http://localhost:8888/api/orders
          uri: lb://order-service    # http://localhost:8888/api/orders 使用负载均衡算法选择机器
        - id: goodsRouter
          predicates:
            - Path=/api/goods/**
          #            - Between=2025-07-01T09:12:29.729+08:00[Asia/Shanghai], 2025-07-01T09:15:29.729+08:00[Asia/Shanghai]
          #            - Header=Authorization
          #            - Parameter=info
          #          uri: http://localhost:9999
          uri: lb://goods-service
          filters:
            #            - StripPrefix=1     # 路径前一个文件夹删掉 http://localhost:9999/goods/
            #            - SetRequestHeader=Authorization, 123456
            #            - ParameterContentReplace=info, helloworld
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 1
                redis-rate-limiter.burstCapacity: 3
                key-resolver: "#{@keyResolver}"   #SpEL表达式
  main:
    allow-bean-definition-overriding: true




