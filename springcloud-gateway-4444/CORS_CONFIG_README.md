# Spring Cloud Gateway 跨域配置说明

## 配置方式

### 方式一：通过 application.yml 配置（推荐）

```yaml
spring:
  cloud:
    gateway:
      globalcors:
        cors-configurations:
          [/**]:
            # 允许的源，不能与 allowCredentials: true 同时使用 "*"
            allowedOriginPatterns: "*"
            # 允许的HTTP方法
            allowedMethods:
              - GET
              - POST
              - PUT
              - DELETE
              - OPTIONS
              - HEAD
              - PATCH
            # 允许的请求头
            allowedHeaders: "*"
            # 是否允许携带认证信息（如 cookies）
            allowCredentials: true
            # 预检请求的缓存时间（秒）
            maxAge: 3600
```

### 方式二：通过 Java 配置类

参考 `CorsConfig.java` 文件中的配置。

## 重要说明

1. **allowedOrigins vs allowedOriginPatterns**
   - `allowedOrigins: "*"` 与 `allowCredentials: true` 不能同时使用
   - 使用 `allowedOriginPatterns: "*"` 可以解决这个问题
   - 生产环境建议指定具体的域名

2. **生产环境配置建议**
   ```yaml
   allowedOriginPatterns: 
     - "https://yourdomain.com"
     - "https://*.yourdomain.com"
   ```

3. **配置优先级**
   - 如果同时配置了 application.yml 和 Java 配置类，Java 配置类的优先级更高
   - 建议只使用一种配置方式，避免冲突

## 常见问题解决

### 1. 跨域请求仍然被阻止
- 检查是否有多个跨域配置冲突
- 确认前端请求的域名是否在允许列表中
- 检查是否有其他过滤器干扰

### 2. 预检请求失败
- 确保 OPTIONS 方法在 allowedMethods 中
- 检查 maxAge 配置是否合理

### 3. 携带 Cookie 的请求失败
- 确保 allowCredentials 设置为 true
- 不能使用 allowedOrigins: "*"，需要使用 allowedOriginPatterns
- 前端请求需要设置 withCredentials: true

## 测试跨域配置

可以使用以下 JavaScript 代码测试跨域配置：

```javascript
// 简单请求测试
fetch('http://localhost:4444/api/orders/list')
  .then(response => response.json())
  .then(data => console.log(data))
  .catch(error => console.error('Error:', error));

// 携带认证信息的请求测试
fetch('http://localhost:4444/api/orders/list', {
  method: 'GET',
  credentials: 'include',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your-token'
  }
})
.then(response => response.json())
.then(data => console.log(data))
.catch(error => console.error('Error:', error));
```
