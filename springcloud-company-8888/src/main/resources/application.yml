server:
  port: 8888
spring:
  application:
    name: company-service
    data:
      redis:
        host: localhost
        port: 6379
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *******************************************************************************************************************
    username: root
    password: 123456
  main:
    allow-bean-definition-overriding: true
    gateway:
      globalcors:
        cors-configurations:
          [ /** ]:
            allowedOriginPatterns: "*"
            allowedMethods:
              - GET
              - POST
              - PUT
              - DELETE
              - OPTIONS
              - HEAD
              - PATCH
      routes:
        - id: goodsRouter
          predicates:
            - Path=/api/company/**
            - Between=2025-07-01T09:12:29.729+08:00[Asia/Shanghai], 2025-07-01T09:15:29.729+08:00[Asia/Shanghai]
          uri: lb://company-service




