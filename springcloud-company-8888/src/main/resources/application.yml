server:
  port: 8888
spring:
  application:
    name: company-service
    data:
      redis:
        host: localhost
        port: 6379
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *******************************************************************************************************************
    username: root
    password: 123456
  main:
    allow-bean-definition-overriding: true





