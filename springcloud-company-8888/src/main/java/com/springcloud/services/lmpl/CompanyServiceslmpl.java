package com.springcloud.services.lmpl;

import com.springcloud.entry.CompanyInfo;
import com.springcloud.mapper.CompanyMapper;
import com.springcloud.services.CompanyServices;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class CompanyServiceslmpl implements CompanyServices {
    @Resource
    private CompanyMapper companyMapper;

    @Override
    public Map<Integer, CompanyInfo> getAllCompany() {
        List<CompanyInfo> companyInfos = companyMapper.selectList(null);
        Map<Integer, CompanyInfo> collect = companyInfos.stream().collect(Collectors.toMap(CompanyInfo::getCid, companyInfo -> companyInfo));
        return collect;
    }
}
