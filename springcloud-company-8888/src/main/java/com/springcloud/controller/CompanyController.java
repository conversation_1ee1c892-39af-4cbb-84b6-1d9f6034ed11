package com.springcloud.controller;

import com.springcloud.entry.CompanyInfo;
import com.springcloud.entry.Result;
import com.springcloud.services.CompanyServices;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping("/api/company")
public class CompanyController {
    @Resource
    private CompanyServices companyServices;

    @GetMapping("/getAllCompany")
    public Result<Map<Integer, CompanyInfo>> getAllCompany() {
        return Result.success(companyServices.getAllCompany());
    }


}
