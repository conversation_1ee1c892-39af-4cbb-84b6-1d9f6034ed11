package com.springcloud.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.springcloud.entry.CarInfo;
import com.springcloud.entry.Result;
import com.springcloud.servers.CarServices;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/car")
public class CarController {

    @Resource
    private CarServices carServices;

    @GetMapping("/getPageCar")
    public Result<IPage<CarInfo>> getPageCar(@RequestParam(defaultValue = "1") Integer pageNum,
                                             @RequestParam(defaultValue = "5") Integer pageSize,
                                             @RequestParam(required = false) Integer cid,
                                             @RequestParam(required = false) String carNum) {
        return Result.success(carServices.getPageCar(pageNum, pageSize, cid, carNum));
    }

    @DeleteMapping("/deleteCar/{id}")
    public Result deleteCar(@PathVariable Integer id) {
        return carServices.deleteCar(id) ? Result.success() : Result.error("删除失败");
    }

    @PutMapping("/updateCar")
    public Result updateCar(@RequestBody CarInfo carInfo) {
        return carServices.updateCar(carInfo) ? Result.success() : Result.error("修改失败");
    }

    @PostMapping("/addCar")
    public Result addCar(@RequestBody CarInfo carInfo) {
        return carServices.addCar(carInfo) ? Result.success() : Result.error("添加失败");
    }


}
