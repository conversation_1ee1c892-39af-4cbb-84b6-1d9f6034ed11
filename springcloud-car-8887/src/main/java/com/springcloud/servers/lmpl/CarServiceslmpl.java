package com.springcloud.servers.lmpl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.springcloud.entry.CarInfo;
import com.springcloud.entry.CompanyInfo;
import com.springcloud.entry.Result;
import com.springcloud.feign.CompanyFeign;
import com.springcloud.mapper.CarMapper;
import com.springcloud.servers.CarServices;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class CarServiceslmpl implements CarServices {

    @Resource
    private CarMapper carMapper;

    @Resource
    private CompanyFeign companyFeign;

    @Override
    public List<CarInfo> getAllCar() {
        return List.of();
    }

    @Override
    public IPage<CarInfo> getPageCar(Integer pageNum, Integer pageSize, Integer cid, String carNum) {
        QueryWrapper<CarInfo> queryWrapper = new QueryWrapper<>();
        //判断cid 是否为空 且为0
        if (cid != null && cid != 0) queryWrapper.eq("cid", cid);
        if (StrUtil.isEmpty(carNum)) queryWrapper.like("carnum", carNum);
        IPage<CarInfo> pageCar = carMapper.getPageCar(new Page<>(pageNum, pageSize), queryWrapper);
        Result<Map<Integer, CompanyInfo>> company = companyFeign.getAllCompany();
        Map<Integer, CompanyInfo> map = company.getData();
        pageCar.getRecords().forEach(carInfo -> {
            CompanyInfo object = map.get(carInfo.getId());
            if (object != null) carInfo.setCname(object.getName());
        });
        return pageCar;
    }

    @Override
    public boolean deleteCar(Integer id) {
        QueryWrapper<CarInfo> queryWrapper = new QueryWrapper<>();
        if (id != null && id > 0) queryWrapper.eq("id", id);
        return carMapper.delete(queryWrapper) > 0;
    }

    @Override
    public boolean updateCar(CarInfo carInfo) {
        QueryWrapper<CarInfo> queryWrapper = new QueryWrapper<>();
        if (carInfo == null) return false;
        if (carInfo.getId() != null && carInfo.getId() > 0) queryWrapper.eq("id", carInfo.getId());
        return carMapper.update(carInfo, queryWrapper) > 0;
    }

    @Override
    public boolean addCar(CarInfo carInfo) {
        if (carInfo == null) return false;
        return carMapper.insert(carInfo) > 0;
    }
}
