package com.springcloud.servers;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.springcloud.entry.CarInfo;

import java.util.List;

public interface CarServices {

    List<CarInfo> getAllCar();

    IPage<CarInfo> getPageCar(Integer pageNum, Integer pageSize, Integer cid, String carnum);

    boolean deleteCar(Integer id);

    boolean updateCar(CarInfo carInfo);

    boolean addCar(CarInfo carInfo);
}
