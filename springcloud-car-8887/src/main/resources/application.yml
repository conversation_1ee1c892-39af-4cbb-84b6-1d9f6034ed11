server:
  port: 8887
spring:
  application:
    name: car-service
    data:
      redis:
        host: localhost
        port: 6379
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *******************************************************************************************************************
    username: root
    password: 123456
  main:
    allow-bean-definition-overriding: true
    web-application-type: reactive

## Feign配置 - 使用OkHttp替代默认的阻塞HTTP客户端
#feign:
#  httpclient:
#    enabled: false
#  okhttp:
#    enabled: true
#  client:
#    config:
#      default:
#        connectTimeout: 5000
#        readTimeout: 5000




