package com.springcloud.feign;

import com.springcloud.entry.CompanyInfo;
import com.springcloud.entry.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.Map;

@FeignClient(
        value = "company-service",
        path = "api/company")
public interface CompanyFeign {

    @GetMapping("/getAllCompany")
    public Result<Map<Integer, CompanyInfo>> getAllCompany();
}
