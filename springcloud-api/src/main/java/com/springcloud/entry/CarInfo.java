package com.springcloud.entry;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@TableName("carinfo")
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class CarInfo {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    @TableField(value = "cartype")
    private long cartype;
    private long cid;
    //不参与sql
    @TableField(exist = false)
    private String cname;
    @TableField(value = "begintime")
    private java.sql.Timestamp begintime;
    @TableField(value = "endtime")
    private java.sql.Timestamp endtime;
    @TableField(value = "carnum")
    private String carnum;
    private String user;
    private String phone;
    @TableField(value = "regtype")
    private long regtype;
}
