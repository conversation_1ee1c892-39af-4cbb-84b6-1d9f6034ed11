package com.springcloud.entry;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@TableName("companyinfo")
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class CompanyInfo {
    @TableId(value = "cid", type = IdType.AUTO)
    private Integer cid;
    private String name;
    private String address;
    private String remark;
}
