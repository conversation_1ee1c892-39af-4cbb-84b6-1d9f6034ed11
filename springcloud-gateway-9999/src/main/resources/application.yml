server:
  port: 9999

spring:
  application:
    name: gateway-service
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
    gateway:
      # 全局CORS配置
      globalcors:
        cors-configurations:
          '[/**]':
            allowedOriginPatterns: "*"
            allowedMethods:
              - GET
              - POST
              - PUT
              - DELETE
              - OPTIONS
              - HEAD
              - PATCH
            allowedHeaders: "*"
            allowCredentials: true
      # 路由配置
      routes:
        # 汽车服务路由
        - id: car-service-route
          uri: lb://car-service
          predicates:
            - Path=/api/car/**
          filters:
            - StripPrefix=0
        
        # 公司服务路由
        - id: company-service-route
          uri: lb://company-service
          predicates:
            - Path=/api/company/**
          filters:
            - StripPrefix=0
      
      # 服务发现配置
      discovery:
        locator:
          enabled: true
          lower-case-service-id: true

# 日志配置
logging:
  level:
    org.springframework.cloud.gateway: DEBUG
    org.springframework.web.reactive: DEBUG
