server:
  port: 5555
spring:
  application:
    name: gateway-service
  redis:
    host: localhost
    port: 6379
  cloud:
    sentinel:
      transport:
        dashboard: localhost:8081
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
    gateway:
      globalcors:
        cors-configurations:
          [ /** ]:
            # ???????? allowCredentials: true ???? "*"
            allowedOriginPatterns: "*"
            # ???HTTP??
            allowedMethods:
              - GET
              - POST
              - PUT
              - DELETE
              - OPTIONS
              - HEAD
              - PATCH
            # ??????
            allowedHeaders: "*"
            # ???????????? cookies?
            allowCredentials: true
            # ????????????
            maxAge: 3600
      routes:
        - id: companyRouter
          predicates:
            - Path=/api/company/**
            - Between=2025-07-01T09:12:29.729+08:00[Asia/Shanghai], 2025-07-01T09:15:29.729+08:00[Asia/Shanghai]
          uri: lb://company-service
        - id: carRouter
          predicates:
            - Path=/api/car/**
            - Between=2025-07-01T09:12:29.729+08:00[Asia/Shanghai], 2025-07-01T09:15:29.729+08:00[Asia/Shanghai]
          uri: lb://car-service

  main:
    allow-bean-definition-overriding: true





